# OpenStory Frontend

This is the frontend service for the OpenStory-World application, built with Next.js.

## Setup

1.  Install dependencies:
    ```bash
    yarn
    # or
    npm install
    ```

2.  Create a `.env` file in the project root with the following variables:
    ```dotenv

    ENVIRONMENT=development 
    # Your environment 
    NODE_ENV=dev 
    # program host
    OPENSTORY_HOST=
    # end_point url
    OPENSTORY_API_ENDPOINT=
    # end_point_api_iot
    OPENSTORY_API_IOT_HOST=
    # cdn url
    OPENSTORY_CDN=
    # if you use next.env,try this 
    NEXT_PUBLIC_APP_ENV=development 

    ```

3.  Run the development server:
    ```bash
    npm run dev
    # or
    yarn dev
    ```
