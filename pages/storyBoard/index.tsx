'use client';

import React, { useState } from 'react';
import Head from 'next/head';
import { NextPage } from 'next';
import NavBar from '@/components/NavBar';
import Chatbot from '@/components/Chatbot/Chatbot';
import SidePanel from '@/components/StoryBoard/SidePanel';
import StoryboardGrid, { StoryboardFrame } from '@/components/StoryBoard/StoryboardGrid';
import { useTaskStore } from '@/store/task';

const StoryBoardPage: NextPage = () => {
  const [sidePanelCollapsed, setSidePanelCollapsed] = useState(false);
  const { frames: sampleFrames } = useTaskStore();
  console.log('useTaskStore', useTaskStore.getState())
  const handleFramesChange = (updatedFrames: StoryboardFrame[]) => {
    console.log('Frames updated:', updatedFrames);
    // Here you would typically update state or call an API
  };

  const handleUpdateShot = (frame: StoryboardFrame) => {
    console.log('Shot update requested:', frame);
    // Here you would call your backend API to regenerate the image
  };

  const handleSidePanelCollapse = (collapsed: boolean) => {
    setSidePanelCollapsed(collapsed);
  };

  return (
    <>
      <Head>
        <title>Story Board | OpenStory</title>
        <meta name="description" content="Create and visualize your story" />
      </Head>

      <div className="min-h-screen bg-gray-50 overflow-hidden">
        <NavBar  activePage="create" />
        
        <div className="fixed inset-0 bg-gray-50 -z-10"></div>
        
        <main className="pt-28 px-0 pb-24 relative flex flex-grow">
          {/* Side Panel - positioned to take full height and show regardless of screen size */}
          <div className="h-[calc(100vh-7rem)] fixed left-0 top-28 z-10">
            <SidePanel 
              onCollapse={handleSidePanelCollapse}
            />
          </div>
          
          {/* Main Storyboard Area - with left margin that adjusts based on SidePanel state */}
          <div className={`flex-1 rounded-xl shadow-sm p-6 overflow-hidden bg-transparent transition-all duration-300 ${
            sidePanelCollapsed ? 'ml-6' : 'ml-[360px] md:ml-[380px]'
          }`}>
            <StoryboardGrid 
              initialFrames={sampleFrames} 
              onChange={handleFramesChange} 
              onUpdateShot={handleUpdateShot}
            />
          </div>
        </main>
        
        {/* Chatbot component with sidebar position */}
        <Chatbot 
          position="sidebar" 
          initialMessage="I've added some stories and characters! Take a look"
        />
      </div>
    </>
  );
};

export default StoryBoardPage;
