import React from 'react';
import InputDesign from '@/components/InputDesign';
import NavBar from '@/components/NavBar';
import { Italiana } from 'next/font/google';

const italiana = Italiana({ weight: '400', subsets: ['latin'] });

const Home = () => {

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-white via-blue-50 to-blue-100 relative">
      {/* NavBar at the top */}
      <NavBar activePage="create" />
      
      {/* Main content */}
      <div className="flex flex-col items-center justify-center w-full h-full pt-48">
        <span style={{ fontSize: 80, marginBottom: 16 }}>🎥</span>
        <h1 className={`text-6xl ${italiana.className} text-blue-400 mb-8`}>Magic Create</h1>
        <div className="flex justify-center w-full max-w-2xl">
          <InputDesign />
        </div>
      </div>
    </div>
  );
};

export default Home;
