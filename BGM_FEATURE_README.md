# BGM功能实现说明

## 功能概述
在Storyboard的左侧添加了BGM（背景音乐）设置面板，支持：
- 下拉选择BGM
- BGM试听功能
- 在创建故事时将BGM信息传递给stories/create接口

## 实现的文件

### 1. 新增文件

#### `src/services/bgmService.ts`
- BGM相关的API服务
- 包含获取BGM列表的接口
- 目前使用模拟数据，包含5首不同类型的BGM

#### `src/components/StoryBoard/BGMSelector.tsx`
- BGM选择组件
- 支持下拉选择BGM
- 支持BGM试听功能
- 显示BGM分类和时长信息

### 2. 修改的文件

#### `src/store/task.ts`
- 在TaskState接口中添加了selectedBgm字段
- 用于存储用户选择的BGM信息

#### `src/components/StoryBoard/StoryboardGrid.tsx`
- 修改布局，在左侧添加BGM选择面板
- 修改generateStoryboard函数，在创建故事时传递BGM信息给API

## BGM数据结构

```typescript
interface BGM {
  id: string;
  name: string;
  url: string;
  category: string;
  duration?: number;
  description?: string;
}
```

## API集成

在创建故事时，BGM信息会作为以下格式传递给`stories/create`接口：

```typescript
{
  ...storyData,
  bgm: {
    id: "1",
    name: "Epic Adventure",
    url: "/bgm/epic-adventure.mp3",
    category: "Adventure"
  }
}
```

如果用户没有选择BGM，bgm字段将为null。

## 使用方法

1. 在Storyboard页面左侧可以看到"Background Music"面板
2. 点击下拉菜单选择BGM
3. 选择BGM后可以点击播放按钮试听
4. 点击"Create Story"按钮时，选择的BGM信息会一起发送给后端

## 注意事项

1. 目前BGM文件使用模拟数据，实际的BGM文件需要放在`public/bgm/`目录下
2. BGM服务目前有错误处理，如果API不存在会返回模拟数据
3. 音频播放使用HTML5 Audio API，支持常见的音频格式（mp3, wav等）

## 后续扩展

1. 可以添加BGM分类筛选功能
2. 可以添加BGM搜索功能
3. 可以添加自定义上传BGM功能
4. 可以添加BGM音量控制功能
