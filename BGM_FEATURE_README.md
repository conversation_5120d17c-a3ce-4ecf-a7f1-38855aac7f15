# BGM功能实现说明

## 功能概述
在Storyboard页面的左侧面板storyline下方添加了BGM（背景音乐）设置功能，支持：
- 下拉选择BGM
- BGM试听功能
- 在创建故事时将BGM信息传递给stories/create接口

## 实现的文件

### 1. 新增文件

#### `src/services/bgmService.ts`

- BGM相关的服务
- 直接返回固定的BGM列表，包含7首不同类型的BGM
- 不需要调用API，使用本地配置

#### `src/components/StoryBoard/BGMSelector.tsx`

- BGM选择组件
- 支持下拉选择BGM
- 在下拉列表的每一行内集成试听按钮
- 简洁的设计，只显示BGM名称和试听按钮
- 优化的用户界面，减小了下拉框高度
- 智能错误处理，选择选项时自动清除错误提示
- 支持"No Background Music"选项，外部显示也会相应更新

### 2. 修改的文件

#### `src/store/task.ts`

- 在TaskState接口中添加了selectedBgm字段
- 用于存储用户选择的BGM信息

#### `src/components/StoryBoard/SidePanel.tsx`

- 在storyline下方添加BGM选择器
- 保持原有的Characters/Assets功能不变

#### `src/components/StoryBoard/StoryboardGrid.tsx`

- 修改generateStoryboard函数，在创建故事时传递BGM信息给API

## BGM数据结构

```typescript
interface BGM {
  id: string;
  name: string;
  url: string;
  category: string;
  duration?: number;
  description?: string;
}
```

## API集成

在创建故事时，BGM信息会作为以下格式传递给`stories/create`接口：

```typescript
{
  ...storyData,
  bgm: {
    id: "1",
    name: "Epic Adventure",
    url: "/bgm/epic-adventure.mp3",
    category: "Adventure"
  }
}
```

如果用户没有选择BGM，bgm字段将为null。

## BGM选项

目前提供了7种不同类型的BGM：

- **drum beats** (Rhythmic类型) - 动感鼓点节拍
- **electronic chill** (Electronic类型) - 轻松电子环境音乐
- **energetic beats** (Upbeat类型) - 高能量节拍，适合动作场景
- **majestic orchestra** (Orchestral类型) - 宏伟管弦乐，适合史诗场景
- **melancholy instrumental** (Emotional类型) - 忧郁器乐，适合情感场景
- **serene piano** (Piano类型) - 宁静钢琴旋律
- **upbeat chill** (Chill类型) - 轻松愉快的背景音乐

## 使用方法

1. 在Storyboard页面左侧面板的storyline下方可以看到"Background Music"面板
2. 点击下拉菜单查看BGM选项
3. 每个BGM选项右侧都有一个播放按钮，可以直接点击试听
4. 点击BGM名称选择该BGM，或选择"No Background Music"不使用背景音乐
5. 选择后下拉框外部会显示当前选择的BGM名称或"No Background Music"
6. 点击"Create Story"按钮时，选择的BGM信息会一起发送给后端

## 注意事项

1. BGM文件配置为固定列表，文件存放在`public/bgm/`目录下
2. 音频播放使用HTML5 Audio API，支持常见的音频格式（mp3, wav等）
3. BGM选择器集成在左侧面板中，不影响原有的storyline和characters/assets功能
4. 试听按钮直接集成在下拉列表中，点击试听按钮不会选择该BGM，需要点击BGM名称才会选择
5. 选择任何选项时会自动清除之前的错误提示
6. 界面经过优化，移除了时长显示和分类标签，下拉框高度更紧凑
7. "No Background Music"选项的字号已调整为14px，与其他选项保持一致
8. 修复了选择"No Background Music"时焦点移动导致的位置变化问题
9. 修复了"Select BGM"标签位置问题，现在作为placeholder显示在选择框内部

## 后续扩展

1. 可以添加BGM分类筛选功能
2. 可以添加BGM搜索功能
3. 可以添加自定义上传BGM功能
4. 可以添加BGM音量控制功能
