import dayjs from 'dayjs';

export function getDateString(isoDate: string | dayjs.Dayjs): string {
  const date = dayjs(isoDate);

  return date.format('YYYY-MM-DD');
}

export function getTimestamp(isoDate: string | dayjs.Dayjs): number {
  const date = dayjs(isoDate);

  return date.valueOf();
}

export function convertToDayjs(dateString: string): dayjs.Dayjs {
  const dayjsObj = dayjs(dateString);
  return dayjsObj;
}

export function formatDateString(inputDate: string): string {
  const date = dayjs(inputDate);

  // 检查日期是否有效
  if (!date.isValid()) {
    throw new Error('Invalid date format. Please provide a date in the format YYYY-MM-DD.');
  }

  // 使用 Intl.DateTimeFormat 格式化日期
  const formattedDate = date.format('DD MMM YYYY');

  return formattedDate;
}

export function calculateDaysBetweenDates(startDate: string, endDate: string): number {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const timeDiff = end.getTime() - start.getTime();
  const daysDiff = timeDiff / (1000 * 3600 * 24);
  return Math.round(daysDiff);
}

export function dateToString(date: Date) {
  return date.toISOString().split('T')[0];
}

export function addDaysToDate(date: Date, days: number) {
  const newDate = new Date(date);
  newDate.setUTCDate(newDate.getUTCDate() + days);
  return newDate;
}
