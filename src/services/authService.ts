import api from '@/lib/api';
import { supabase } from '@/lib/supabase';

export const exchangeToken = async () => {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) {
      throw new Error('No session found');
    }

    // 使用Supabase的token换取后端的token
    const response = await api.post('/auth/exchange-token', {
      supabase_token: session.access_token
    });

    // 存储后端token
    localStorage.setItem('backend_token', response.data.token);
    return response.data.token;
  } catch (error) {
    console.error('Failed to exchange token:', error);
    throw error;
  }
};

export const getBackendToken = async () => {
  let token = localStorage.getItem('backend_token');
  if (!token) {
    token = await exchangeToken();
  }
  return token;
}; 