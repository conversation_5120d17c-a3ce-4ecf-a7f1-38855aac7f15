import axios from 'axios';
import { useAuthStore } from '@/store/authStore';

const API_HOST = process.env.NEXT_PUBLIC_API_HOST || 'http://localhost:8000/api/v1';

export interface VoiceLocale {
  id: string;
  display_name: string;
  description: string;
  voice_id: string;
  gender: 'male' | 'female' | 'neutral';
  age: number;
  accent: string;
  created_at: string;
  updated_at: string;
}

export interface VoiceLocalesResponse {
  items: VoiceLocale[];
  total: number;
}

export const voiceLocaleService = {
  async getVoiceLocales(userId: string, skip: number = 0, limit: number = 10): Promise<VoiceLocalesResponse> {
    try {
      const token = useAuthStore.getState().token;
      const response = await axios.get(`${API_HOST}/voice-locales/`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        params: {
          user_id: userId,
          skip,
          limit
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching voice locales:', error);
      throw error;
    }
  },

  async createVoiceLocale(voiceLocale: Omit<VoiceLocale, 'id' | 'created_at' | 'updated_at'>): Promise<VoiceLocale> {
    try {
      const token = useAuthStore.getState().token;
      const response = await axios.post(`${API_HOST}/voice-locales/`, voiceLocale, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error creating voice locale:', error);
      throw error;
    }
  },

  async updateVoiceLocale(id: string, updates: Partial<VoiceLocale>): Promise<VoiceLocale> {
    try {
      const token = useAuthStore.getState().token;
      const response = await axios.patch(`${API_HOST}/voice-locales/${id}`, updates, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error updating voice locale:', error);
      throw error;
    }
  },

  async deleteVoiceLocale(id: string): Promise<void> {
    try {
      const token = useAuthStore.getState().token;
      await axios.delete(`${API_HOST}/voice-locales/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
    } catch (error) {
      console.error('Error deleting voice locale:', error);
      throw error;
    }
  },

  async getVoiceLocaleById(id: string): Promise<VoiceLocale> {
    try {
      const token = useAuthStore.getState().token;
      const response = await axios.get(`${API_HOST}/voice-locales/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching voice locale by id:', error);
      throw error;
    }
  },

  async searchVoiceLocales(searchTerm: string, skip: number = 0, limit: number = 10): Promise<VoiceLocalesResponse> {
    try {
      const token = useAuthStore.getState().token;
      const response = await axios.get(`${API_HOST}/voice-locales/search`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        params: {
          search_term: searchTerm,
          skip,
          limit
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error searching voice locales:', error);
      throw error;
    }
  }
}; 