import axios from 'axios';
import { useAuthStore } from '@/store/authStore';

const API_HOST = process.env.NEXT_PUBLIC_API_HOST || 'http://localhost:8000/api/v1';

export interface BGM {
  id: string;
  name: string;
  url: string;
  category: string;
  duration?: number;
  description?: string;
}

export interface BGMResponse {
  items: BGM[];
  total: number;
}

export const bgmService = {
  async getBGMList(skip: number = 0, limit: number = 50): Promise<BGMResponse> {
    try {
      const token = useAuthStore.getState().token;
      const response = await axios.get(`${API_HOST}/bgm/list`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        params: {
          skip,
          limit
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching BGM list:', error);
      // 如果API不存在，返回模拟数据
      return {
        items: [
          {
            id: '1',
            name: 'Epic Adventure',
            url: '/bgm/epic-adventure.mp3',
            category: 'Adventure',
            duration: 180,
            description: 'An epic orchestral piece perfect for adventure scenes'
          },
          {
            id: '2',
            name: 'Romantic Melody',
            url: '/bgm/romantic-melody.mp3',
            category: 'Romance',
            duration: 150,
            description: 'A gentle romantic melody for love scenes'
          },
          {
            id: '3',
            name: 'Suspense Theme',
            url: '/bgm/suspense-theme.mp3',
            category: 'Thriller',
            duration: 120,
            description: 'Tension-building music for suspenseful moments'
          },
          {
            id: '4',
            name: 'Happy Days',
            url: '/bgm/happy-days.mp3',
            category: 'Comedy',
            duration: 90,
            description: 'Upbeat and cheerful music for happy scenes'
          },
          {
            id: '5',
            name: 'Dark Atmosphere',
            url: '/bgm/dark-atmosphere.mp3',
            category: 'Horror',
            duration: 200,
            description: 'Dark and mysterious background music'
          }
        ],
        total: 5
      };
    }
  },

  async getBGMByCategory(category: string): Promise<BGM[]> {
    try {
      const token = useAuthStore.getState().token;
      const response = await axios.get(`${API_HOST}/bgm/category/${category}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching BGM by category:', error);
      // 返回模拟数据
      const allBgm = await this.getBGMList();
      return allBgm.items.filter(bgm => bgm.category === category);
    }
  }
};
