import axios from "axios";
import { useAuthStore } from "@/store/authStore";

const API_HOST =
  process.env.NEXT_PUBLIC_API_HOST || "http://localhost:8000/api/v1";

export interface BGM {
  id: string;
  name: string;
  url: string;
  category: string;
  duration?: number;
  description?: string;
}

export interface BGMResponse {
  items: BGM[];
  total: number;
}

export const bgmService = {
  getBGMList(): BGMResponse {
    // 直接返回固定的BGM列表，不需要调用API
    return {
      items: [
        {
          id: "1",
          name: "drum beats",
          url: "/bgm/drum-beats.mp3",
          category: "Rhythmic",
          duration: 120,
          description: "Energetic drum patterns for dynamic scenes",
        },
        {
          id: "2",
          name: "electronic chill",
          url: "/bgm/electronic-chill.mp3",
          category: "Electronic",
          duration: 180,
          description: "Relaxed electronic ambient music",
        },
        {
          id: "3",
          name: "energetic beats",
          url: "/bgm/energetic-beats.mp3",
          category: "Upbeat",
          duration: 150,
          description: "High-energy beats for action scenes",
        },
        {
          id: "4",
          name: "majestic orchestra",
          url: "/bgm/majestic-orchestra.mp3",
          category: "Orchestral",
          duration: 240,
          description: "Grand orchestral composition for epic moments",
        },
        {
          id: "5",
          name: "melancholy instrumental",
          url: "/bgm/melancholy-instrumental.mp3",
          category: "Emotional",
          duration: 200,
          description: "Touching instrumental piece for emotional scenes",
        },
        {
          id: "6",
          name: "serene piano",
          url: "/bgm/serene-piano.mp3",
          category: "Piano",
          duration: 160,
          description: "Peaceful piano melody for calm moments",
        },
        {
          id: "7",
          name: "upbeat chill",
          url: "/bgm/upbeat-chill.mp3",
          category: "Chill",
          duration: 140,
          description: "Uplifting yet relaxed background music",
        },
      ],
      total: 7,
    };
  },
};
