import axios from 'axios';
import { useAuthStore } from '@/store/authStore';

const API_HOST = process.env.NEXT_PUBLIC_API_HOST || 'http://localhost:8000/api/v1';

export const storyBoardService = {
  async getStories(user_id: string, skip: number = 0, limit: number = 10): Promise<any> {
    try {
      const token = useAuthStore.getState().token;
      const response = await axios.get(`${API_HOST}/stories/getStoriesByUserId`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        params: {
          user_id: user_id,
          skip,
          limit
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching characters:', error);
      throw error;
    }
  },

  async createStory(story: any): Promise<any> {
    try {
      const token = useAuthStore.getState().token;
      const response = await axios.post(`${API_HOST}/stories/create`, story, {
        headers: {
          'Authorization': `Bear<PERSON> ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error creating character:', error);
      throw error;
    }
  },

async saveStory(data: any): Promise<any> {
  try {
    const token = useAuthStore.getState().token;
    const response = await axios.post(`${API_HOST}/stories/save`, data, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response;
  } catch (error) {
    console.error('Error saveing story:', error);
    throw error;
  }
},

async updateStory(data: any): Promise<any> {
  try {
    const token = useAuthStore.getState().token;
    const response = await axios.post(`${API_HOST}/stories/update`, data, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    console.log('response------', response);
    return response;
  } catch (error) {
    console.error('Error update story:', error);
    throw error;
  }
}
}; 
