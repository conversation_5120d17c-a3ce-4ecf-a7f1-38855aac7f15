import axios from 'axios';
import { useAuthStore } from '@/store/authStore';

const API_HOST = process.env.NEXT_PUBLIC_API_HOST || 'http://localhost:8000/api/v1';

export interface CharacterItem {
    name: string;
    gender?: string;
    age?: number;
    oneliner?: string;
    backstory?: string;
    personality?: string;
    appearance?: string;
    preview_img?: string;
    is_public?: boolean;
    user_id: string;
    voice_description?: string;
    visual_style?: any;
}

export interface CharacterResponse {
  data: any[];
}

export const characterService = {
  async getCharacter(user_id: string, skip: number = 0, limit: number = 10): Promise<CharacterResponse> {
    try {
      const token = useAuthStore.getState().token;
      const response = await axios.get(`${API_HOST}/characters/getCharactersByUserId`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        params: {
          user_id: user_id,
          skip,
          limit
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching characters:', error);
      throw error;
    }
  },

  async createCharacter(character: Omit<CharacterItem, 'preview_img'>): Promise<CharacterItem> {
    try {
      const token = useAuthStore.getState().token;
      const response = await axios.post(`${API_HOST}/characters/create`, character, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error creating character:', error);
      throw error;
    }
  }
}; 