import api from '@/lib/api';

const API_HOST = process.env.NEXT_PUBLIC_API_HOST || 'http://localhost:8000/api/v1/auth';

export interface UserProfile {
  id: string;
  username: string;
  email: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

export const getUserProfile = async (): Promise<UserProfile> => {
  try {
    const response = await api.get('/auth/profile');
    return response.data;
  } catch (error) {
    console.error('Failed to fetch user profile:', error);
    throw error;
  }
}; 