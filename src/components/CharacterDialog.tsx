import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Box,
  FormHelperText,
  Snackbar,
  Alert,
} from '@mui/material';
import { characterService } from '@/services/characterService';
import { useAuthStore } from '@/store/authStore';
import { LoadingButton } from '@mui/lab';
import { VoiceSelect } from './VoiceSelect';
import { useTaskStore } from '@/store/task';

interface CharacterDialogProps {
  voices: any[];
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export const CharacterDialog: React.FC<CharacterDialogProps> = ({voices, open, onClose, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    gender: '',
    age: '',
    oneliner: '',
    backstory: '',
    personality: '',
    appearance: '',
    voice_description: '',
    voice_id: '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      const user = useAuthStore.getState().user;
      if (!user) throw new Error('User not found');

      await characterService.createCharacter({
        ...formData,
        age: parseInt(formData.age),
        user_id: user.id,
        visual_style: useTaskStore.getState().movie_style["Visual Style"]
      });
      setShowSuccess(true);
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error creating character:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleClose = () => {
    setShowSuccess(false);
  };

  return (
    <>
      <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
        <DialogTitle>Create New Character</DialogTitle>
        <form onSubmit={handleSubmit}>
          <DialogContent>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Character Name"
                    placeholder="Enter a full name that reflects the character's background and culture"
                    value={formData.name}
                    onChange={(e) => handleChange('name', e.target.value)}
                    required
                    sx={{ color: '#999'}}
                    helperText="Choose a name that fits the character's background and setting"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth required>
                    <InputLabel>Character Gender</InputLabel>
                    <Select
                      value={formData.gender}
                      label="Character Gender"
                      onChange={(e) => handleChange('gender', e.target.value)}
                    >
                      <MenuItem value="male">Male</MenuItem>
                      <MenuItem value="female">Female</MenuItem>
                      <MenuItem value="non-binary">Non-binary</MenuItem>
                    </Select>
                    <FormHelperText>Select the character's gender identity</FormHelperText>
                  </FormControl>
                </Grid>
              </Grid>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    type="number"
                    label="Character Age"
                    placeholder="Enter age between 18-90"
                    value={formData.age}
                    onChange={(e) => handleChange('age', e.target.value)}
                    inputProps={{ min: 18, max: 90 }}
                    required
                    helperText="Age should be realistic for the character's role"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth required>
                    <InputLabel>Voice Type</InputLabel>
                    <Select
                      value={formData.voice_description}
                      label="Voice Type"
                      onChange={(e) => handleChange('voice_description', e.target.value)}
                    >
                      <MenuItem value="US female child">US female child</MenuItem>
                      <MenuItem value="US female adult">US female adult</MenuItem>
                      <MenuItem value="US female elderly">US female elderly</MenuItem>
                      <MenuItem value="US male child">US male child</MenuItem>
                      <MenuItem value="US male adult">US male adult</MenuItem>
                      <MenuItem value="US male elderly">US male elderly</MenuItem>
                    </Select>
                    <FormHelperText>Choose a voice that matches the character's age and gender</FormHelperText>
                  </FormControl>
                </Grid>
              </Grid>

              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <VoiceSelect
                    value={formData.voice_id}
                    onChange={(value) => handleChange('voice_id', value)}
                    voices={voices}
                    required
                  />
                </Grid>
              </Grid>

              <TextField
                fullWidth
                label="Character Tagline"
                placeholder="Write a compelling one-liner that captures the character's essence"
                value={formData.oneliner}
                onChange={(e) => handleChange('oneliner', e.target.value)}
                required
                helperText="A memorable phrase that defines the character's role or personality"
              />

              <TextField
                fullWidth
                label="Character Backstory"
                placeholder="Describe the character's past, key life events, and what shaped them into who they are today"
                value={formData.backstory}
                onChange={(e) => handleChange('backstory', e.target.value)}
                multiline
                rows={3}
                required
                helperText="Include their background, motivations, and significant life events"
              />

              <TextField
                fullWidth
                label="Personality Traits"
                placeholder="Describe how the character thinks, feels, and behaves in different situations"
                value={formData.personality}
                onChange={(e) => handleChange('personality', e.target.value)}
                multiline
                rows={3}
                required
                helperText="Include their temperament, values, and behavioral patterns"
              />

              <TextField
                fullWidth
                label="Physical Appearance"
                placeholder="Describe the character's physical features, style, and distinctive characteristics"
                value={formData.appearance}
                onChange={(e) => handleChange('appearance', e.target.value)}
                multiline
                rows={3}
                required
                helperText="Include their physical features, clothing style, and distinctive characteristics"
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={onClose}>Cancel</Button>
            <LoadingButton
              type="submit"
              variant="contained"
              loading={loading}
            >
              Create Character
            </LoadingButton>
          </DialogActions>
        </form>
      </Dialog>

      <Snackbar
        open={showSuccess}
        autoHideDuration={6000}
        onClose={handleClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleClose} severity="success" sx={{ width: '100%' }}>
          Character created successfully!
        </Alert>
      </Snackbar>
    </>
  );
}; 