"use client";
import React, { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import { X } from "lucide-react";
import Image from "next/image";
import { getTagImage } from "@/constants/tagImages";
import { movieStyleService } from "@/services/movieStyleService";
import { useTaskStore } from "@/store/task";
import { generateEpisodeService, generateStoryService } from "@/services/generateStory";
import { characterService } from "@/services/characterService";
import { useAuthStore } from "@/store/authStore";
import ActionButton from "@/components/ui/ActionButton";
import { frameService } from "@/services/frameService";
import { Backdrop, CircularProgress, Box, Typography } from '@mui/material';
import { storyBoardService } from "@/services/storyBoardService";

interface TagOption {
  tag: string;
  options: string[];
}

interface InputDesignProps {
  onCreateStory?: () => void;
  initialValue?: string;
  onChange?: (value: string) => void;
}

const InputDesign: React.FC<InputDesignProps> = ({
  initialValue = "",
  onChange,
}) => {
  const router = useRouter();
  const [inputValue, setInputValue] = useState(initialValue);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [selectedValues, setSelectedValues] = useState<Record<string, string | null>>({
    "Use Case": null,
    "Visual Style": null,
    "World Remix": null,
    "Vibes": null,
  });

  const [tagOptions, setTagOptions] = useState<TagOption[]>([]);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState(false);
  const [currentTask, setCurrentTask] = useState('');
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const fetchMovieStyles = async () => {
      try {
        const response: any = await movieStyleService.getMovieStyles(0, 10);
        if (response && response.length > 0) {
          const newTagOptions: TagOption[] = [];
          response.forEach((style: any) => {
            newTagOptions.push(style.styles);
          });
          setTagOptions(newTagOptions);
        }

      } catch (err) {
        console.error('Error fetching movie styles:', err);
      } finally {
        // setLoading(false);
      }
    };

    fetchMovieStyles();
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setTimeout(() => {
          setActiveDropdown(null);
        }, 50);
      }
    };

    if (activeDropdown) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [activeDropdown]);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(e.target.value);
    if (onChange) {
      onChange(e.target.value);
    }
  };

  const handleTagClick = (tag: string) => {
    setTimeout(() => {
      setActiveDropdown(activeDropdown === tag ? null : tag);
    }, 0);
  };

  const handleOptionSelect = (tag: string, option: string) => {
    setSelectedValues({
      ...selectedValues,
      [tag]: option,
    });
    setActiveDropdown(null);
  };

  const handleRemoveSelection = (tag: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedValues({
      ...selectedValues,
      [tag]: null,
    });
  };

  const handleTestSaveStory = async () => {
    const user = useAuthStore.getState().user;
    await storyBoardService.saveStory({
      id: '',
      user_id: user?.id,
      story: { ...useTaskStore.getState() }
    });
  }

  const handleCreateStory = async () => {
    setLoading(true);
    setProgress(0);
    const user = useAuthStore.getState().user;
    if (!inputValue) return;
    try {
      setCurrentTask('Initializing story creation...');
      useTaskStore.setState({
        id: '',
        movie_style: selectedValues,
        concept: inputValue,
      });

      setProgress(10);
      setCurrentTask('Generating episode...');
      const movieStyleStr = Object.values(selectedValues).filter(Boolean).join(",");
      const response: any = await generateEpisodeService.generateEpisode(inputValue, movieStyleStr);
      console.log(response);

      setProgress(30);
      setCurrentTask('Creating characters...');
      let characters: any[] = [];
      if (response?.characters) {
        characters = await Promise.all(
          response.characters.map(async (character: any) => {
            return await characterService.createCharacter({ ...character, user_id: user?.id, visual_style: useTaskStore.getState().movie_style["Visual Style"]});
          })
        );
        useTaskStore.setState({
          characters: characters
        });
      }
      
      setProgress(50);
      setCurrentTask('Generating story...');
      const story = await generateStoryService.generateStory(characters, response?.show?.synopsis, movieStyleStr);
      console.log(story);
      useTaskStore.setState({
        story_line: response?.show?.synopsis
      });

      setProgress(70);
      setCurrentTask('Creating frames...');
      const frames = await Promise.all(
        story.shots.map(async (frame: any) => {
          const character_dialogue = frame.character_dialogue && frame.character_dialogue.line ? frame.character_dialogue.line : '';
          const result = await frameService.createFrame(
            {
              ...frame,
              id: frame.shot_number,
              characters: characters,
              movie_style: movieStyleStr,
              character_dialogue: character_dialogue,
              character_composition: frame.character_composition ? JSON.stringify(frame.character_composition) : ""
            });
          return { ...frame, image_url: result?.video_url }
        })
      );

      setProgress(90);
      setCurrentTask('Finalizing...');

      useTaskStore.setState({
        frames: frames
      });

      setProgress(100);
      setCurrentTask('Complete!');
      setTimeout(() => {
        setLoading(false);
        router.push('/storyBoard');
      }, 500);

    } catch (error) {
      console.error('Error creating story:', error);
      setLoading(false);
    }
  };

  return (
    <>
      <Backdrop
        sx={{ 
          color: '#fff', 
          zIndex: (theme) => theme.zIndex.drawer + 1,
          flexDirection: 'column',
          gap: 2
        }}
        open={loading}
      >
        <CircularProgress color="inherit" />
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="h6" sx={{ mb: 1 }}>
            {currentTask}
          </Typography>
          <Typography variant="body2" color="inherit">
            Progress: {progress}%
          </Typography>
        </Box>
      </Backdrop>

      <div className="w-full min-w-[320px] sm:min-w-[500px] md:min-w-[600px] lg:w-1/2 xl:w-1/2 2xl:w-1/2 mx-auto flex flex-col gap-6 p-6 md:p-8 bg-white rounded-3xl shadow-sm">
        <textarea
          className="self-stretch text-large leading-relaxed h-[90px] text-slate-600 w-full resize-none outline-none border-none"
          placeholder="What would you like to create?"
          value={inputValue}
          onChange={handleInputChange}
        />
        <div className="w-full h-px bg-gray-100" />
        <div className="flex justify-between items-center self-stretch w-full max-sm:flex-col max-sm:gap-4" ref={dropdownRef}>
          <div className="flex flex-wrap gap-3 items-center">
            {tagOptions.map((tagOption) => (
              <div key={tagOption.tag} className="relative">
                <button
                  onClick={() => handleTagClick(tagOption.tag)}
                  className={`px-4 py-2 text-sm font-base text-center rounded-full border flex items-center gap-2 relative ${selectedValues[tagOption.tag]
                    ? "bg-white text-slate-700 border-blue-400 border-2"
                    : activeDropdown === tagOption.tag
                      ? "bg-gray-100 text-slate-700 border-gray-200"
                      : "bg-white text-slate-700 border-gray-200 hover:border-gray-300"
                    }`}
                >
                  {selectedValues[tagOption.tag] ? selectedValues[tagOption.tag] : tagOption.tag}
                  {selectedValues[tagOption.tag] && (
                    <>
                      <div className="w-2 h-2 rounded-full bg-blue-400 ml-1" />
                      <span
                        className="ml-1 text-slate-700 hover:text-gray-500"
                        onClick={(e) => handleRemoveSelection(tagOption.tag, e)}
                      >
                        <X size={16} />
                      </span>
                    </>
                  )}
                </button>

                {activeDropdown === tagOption.tag && (
                  <div className="fixed z-[100] mt-1 py-4 px-3 bg-white rounded-xl shadow-xl border border-gray-200 max-h-[300px] overflow-y-auto"
                    style={{
                      width: "400px",
                      top: dropdownRef.current ? `${window.scrollY + dropdownRef.current.getBoundingClientRect().bottom + 5}px` : '0px',
                      left: dropdownRef.current ? `${dropdownRef.current.getBoundingClientRect().left}px` : '0px'
                    }}>
                    <div className="grid grid-cols-4 gap-2">
                      {tagOption.options.map((option) => (
                        <div
                          key={option}
                          className="flex flex-col items-center p-2 rounded-md border border-gray-100 hover:border-blue-400 cursor-pointer transition-all"
                          onClick={() => handleOptionSelect(tagOption.tag, option)}
                        >
                          <div className="w-full aspect-square rounded-md overflow-hidden relative group">
                            <Image
                              src={getTagImage(tagOption.tag, option)}
                              alt={option}
                              fill
                              className="object-cover"
                            />
                            <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                              <span className="text-white text-lg font-base text-center p-1">{option}</span>
                            </div>
                          </div>
                          <div className="text-lg font-base mt-1 text-center line-clamp-1" title={option}>{option}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
          <div>
            <ActionButton  onClick={handleCreateStory} >
              Create my story
            </ActionButton>
          </div>
        </div>
      </div>
    </>
  );
};

export default InputDesign;
