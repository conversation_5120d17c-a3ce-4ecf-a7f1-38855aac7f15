import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import styled from 'styled-components';
import { Save, Edit2, X, ChevronLeft, ChevronRight } from 'lucide-react';
import { koho } from '@/theme';
import { useTaskStore } from '@/store/task';
import { useAuthStore } from '@/store/authStore';
import { characterService } from '@/services/characterService';
import { CharacterDialog } from '../CharacterDialog';
import { CharacterDetailDialog } from '../CharacterDetailDialog';
import { Button } from '@mui/material';
import { voiceLocaleService } from '@/services/voiceLocaleService';
import { AssetPreview } from './AssetPreview';
import { BGMSelector } from "./BGMSelector";
// Radio selector component
const StyledRadioWrapper = styled.div`
  .radio-inputs {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    border-radius: 0.5rem;
    background-color: #eee;
    box-sizing: border-box;
    box-shadow: 0 0 0px 1px rgba(0, 0, 0, 0.06);
    padding: 0.25rem;
    width: 100%;
    font-size: 14px;
  }

  .radio-inputs .radio {
    flex: 1 1 auto;
    text-align: center;
  }

  .radio-inputs .radio input {
    display: none;
  }

  .radio-inputs .radio .name {
    display: flex;
    cursor: pointer;
    align-items: center;
    justify-content: center;
    border-radius: 0.5rem;
    border: none;
    padding: 0.5rem 0;
    color: rgba(51, 65, 85, 1);
    transition: all 0.15s ease-in-out;
  }

  .radio-inputs .radio input:checked + .name {
    background-color: #fff;
    font-weight: 600;
  }
`;

// Editable item styling
const EditableBox = styled.div`
  position: relative;
  padding: 12px;
  border-radius: 8px;
  transition: background 0.2s;
  width: 100%;
  min-height: 120px;
  max-height: 400px;
  overflow-y: auto;

  &:hover {
    background: #f5f7fa;
  }

  .edit-icon {
    position: absolute;
    top: 12px;
    right: 12px;
    opacity: 0;
    transition: opacity 0.2s;
  }

  &:hover .edit-icon {
    opacity: 1;
  }

  .edit-controls {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    justify-content: flex-end;
  }

  .edit-controls button {
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
  }
`;

// Styled panel container for collapsible functionality
const PanelContainer = styled.div`
  position: relative;
  height: 100%;
  transition: transform 0.3s ease;
`;

const ToggleButton = styled.button`
  position: absolute;
  z-index: 20;
  top: 50%;
  transform: translateY(-50%);
  background-color: white;
  border: 1px solid #e3e6ea;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 2px 4px rgba(25, 33, 61, 0.1);
  cursor: pointer;

  &:hover {
    background-color: #f8f9fa;
  }
`;

interface StorylineItem {
  id: number;
  text: string;
}

interface Character {
  id: string;
  imageUrl: string;
}

interface Asset {
  id: string;
  imageUrl: string;
}

interface SidePanelProps {
  storyline?: string | StorylineItem[];
  characters?: Character[];
  assets?: Asset[];
  onStorylineUpdate?: (newStoryline: string) => void;
  className?: string;
  onCollapse?: (collapsed: boolean) => void;
}

const SidePanel: React.FC<SidePanelProps> = ({ className, onCollapse }) => {
  const [activeSection, setActiveSection] = useState<"characters" | "assets">(
    "characters"
  );
  const [isEditing, setIsEditing] = useState(false);
  const [collapsed, setCollapsed] = useState(false);
  const [showCharacterDialog, setShowCharacterDialog] = useState(false);
  const [selectedCharacter, setSelectedCharacter] = useState<any>(null);
  const [showCharacterDetail, setShowCharacterDetail] = useState(false);
  const [assets, setAssets] = useState([]);
  const { characters } = useTaskStore();
  const { story_line: storyLine } = useTaskStore();
  const { user } = useAuthStore();

  useEffect(() => {
    const fetchVoices = async () => {
      const user_id = user?.id as string;
      if (user_id) {
        try {
          const response: any = await voiceLocaleService.getVoiceLocales(
            user_id,
            0,
            10
          );
          if (response && response.length > 0) {
            console.log("--", response);
            setAssets(response);
          }
        } catch (err) {
          console.error("Error fetching assets:", err);
        } finally {
          // setLoading(false);
        }
      }
    };

    fetchVoices();
  }, [user]);

  const handleEditClick = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    setIsEditing(false);
  };

  const handleCancel = () => {
    setIsEditing(false);
  };

  const toggleCollapse = () => {
    const newCollapsed = !collapsed;
    setCollapsed(newCollapsed);
    if (onCollapse) {
      onCollapse(newCollapsed);
    }
  };

  const handleCharacterSuccess = async () => {
    const user_id = user?.id as string;
    if (user_id) {
      try {
        const response: any = await characterService.getCharacter(
          user_id,
          0,
          10
        );
        if (response && response.length > 0) {
          //@ts-ignore
          useTaskStore.setState({ ...characters, response });
        }
      } catch (err) {
        console.error("Error refreshing characters:", err);
      }
    }
  };

  const getImgUrl = (character: any) => {
    if (character && character.avatar_urls) {
      return character?.avatar_urls?.url;
    } else {
      return "/placeholder-image.svg";
    }
  };

  const handleCharacterClick = (character: any) => {
    setSelectedCharacter(character);
    setShowCharacterDetail(true);
  };

  return (
    <PanelContainer
      style={{ transform: collapsed ? "translateX(-100%)" : "translateX(0)" }}
    >
      {/* Collapse/Expand button */}
      {collapsed ? (
        <ToggleButton
          onClick={toggleCollapse}
          style={{ right: "-12px" }}
          aria-label="Expand panel"
        >
          <ChevronRight size={16} color="#2388FF" />
        </ToggleButton>
      ) : (
        <ToggleButton
          onClick={toggleCollapse}
          style={{ right: "-12px" }}
          aria-label="Collapse panel"
        >
          <ChevronLeft size={16} color="#2388FF" />
        </ToggleButton>
      )}

      <div
        className={cn(
          "w-[280px] md:w-[360px] flex flex-col gap-6 text-sm h-full",
          "bg-white shadow-md border-r border-[#E3E6EA] py-8 px-6",
          koho.className,
          className
        )}
      >
        {/* Storyline Section */}
        <div className="bg-white rounded-xl shadow-sm flex flex-col flex-shrink-0">
          <div className="p-4 text-center border-b text-gray-800 text-sm font-medium">
            Storyline
          </div>
          <div className="p-4">
            <EditableBox>
              {isEditing ? (
                <div>
                  <textarea
                    value={storyLine}
                    onChange={(e) => {
                      console.log("story line e.target.value", e.target.value);
                      useTaskStore.setState({ story_line: e.target.value });
                    }}
                    className="w-full border border-gray-300 rounded p-2 text-sm text-gray-600 min-h-[120px]"
                    style={{ maxWidth: "400px" }}
                    autoFocus
                  />
                  <div className="edit-controls">
                    <button
                      onClick={handleCancel}
                      className="text-gray-500 hover:bg-gray-100 text-sm"
                    >
                      <X size={12} />
                      Discard
                    </button>
                    <button
                      onClick={handleSave}
                      className="bg-blue-500 text-white hover:bg-blue-600 text-sm"
                    >
                      <Save size={12} />
                      Save
                    </button>
                  </div>
                </div>
              ) : (
                <div className="relative">
                  <p className="text-gray-600 text-sm whitespace-pre-wrap break-words">
                    {storyLine}
                  </p>
                  <button
                    className="edit-icon text-gray-400 hover:text-blue-500 bg-transparent border-0 cursor-pointer"
                    onClick={handleEditClick}
                    aria-label="Edit"
                  >
                    <Edit2 size={12} />
                  </button>
                </div>
              )}
            </EditableBox>
          </div>
        </div>

        {/* Characters/Assets Section */}
        <div className="bg-white rounded-xl shadow-sm flex flex-col flex-grow overflow-hidden">
          {/* Radio Selector */}
          <div className="p-4">
            <StyledRadioWrapper>
              <div className="radio-inputs">
                <label className="radio">
                  <input
                    type="radio"
                    name="asset-type"
                    checked={activeSection === "characters"}
                    onChange={() => setActiveSection("characters")}
                  />
                  <span className="name text-sm">Characters</span>
                </label>
                <label className="radio">
                  <input
                    type="radio"
                    name="asset-type"
                    checked={activeSection === "assets"}
                    onChange={() => setActiveSection("assets")}
                  />
                  <span className="name text-sm">Assets</span>
                </label>
              </div>
            </StyledRadioWrapper>
          </div>

          {/* Content Area */}
          <div className="p-4 flex-1 overflow-y-auto">
            {activeSection === "characters" && (
              <div className="flex flex-col items-center gap-8">
                <div className="w-full grid grid-cols-3 gap-2">
                  {characters.map((character: any) => (
                    <div
                      key={character?.id}
                      className="aspect-square rounded-md bg-gray-100 relative cursor-pointer hover:opacity-90 transition-opacity"
                      onClick={() => handleCharacterClick(character)}
                    >
                      <Image
                        src={getImgUrl(character)}
                        alt="Character"
                        width={104}
                        height={104}
                        className="object-cover w-full h-full"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src =
                            "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='80' viewBox='0 0 24 24' fill='none' stroke='%23cccccc' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M14.5 3.5C14.5 5.43 12.93 7 11 7C9.07 7 7.5 5.43 7.5 3.5C7.5 1.57 9.07 0 11 0C12.93 0 14.5 1.57 14.5 3.5Z'%3E%3C/path%3E%3Cpath d='M11 9C6.03 9 2 13.03 2 18H20C20 13.03 15.97 9 11 9Z'%3E%3C/path%3E%3C/svg%3E";
                        }}
                      />
                    </div>
                  ))}
                </div>
                <Button
                  variant="contained"
                  onClick={() => setShowCharacterDialog(true)}
                >
                  {"Create New Charachter"}
                </Button>
              </div>
            )}

            {activeSection === "assets" && (
              <div className="grid grid-cols-3 gap-2">
                {assets.map((asset, index) => (
                  <AssetPreview key={index} asset={asset} />
                ))}
              </div>
            )}
          </div>
        </div>

        {/* BGM Section */}
        <BGMSelector />
      </div>

      <CharacterDialog
        voices={assets}
        open={showCharacterDialog}
        onClose={() => setShowCharacterDialog(false)}
        onSuccess={handleCharacterSuccess}
      />

      <CharacterDetailDialog
        open={showCharacterDetail}
        onClose={() => setShowCharacterDetail(false)}
        character={selectedCharacter}
      />
    </PanelContainer>
  );
};

export default SidePanel;