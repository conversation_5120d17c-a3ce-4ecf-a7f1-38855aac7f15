import React, { useState, useEffect, useRef } from "react";
import {
  FormControl,
  Select,
  MenuItem,
  Box,
  IconButton,
  Typography,
  Alert,
} from "@mui/material";
import { PlayArrow, Pause } from "@mui/icons-material";
import { useTaskStore } from "@/store/task";

// BGM接口定义
interface BGM {
  id: string;
  name: string;
  url: string;
  category: string;
  duration?: number;
  description?: string;
}

// 直接配置BGM列表
const BGM_LIST: BGM[] = [
  {
    id: "1",
    name: "drum beats",
    url: "/bgm/drum-beats.mp3",
    category: "Rhythmic",
    duration: 120,
    description: "Energetic drum patterns for dynamic scenes",
  },
  {
    id: "2",
    name: "electronic chill",
    url: "/bgm/electronic-chill.mp3",
    category: "Electronic",
    duration: 180,
    description: "Relaxed electronic ambient music",
  },
  {
    id: "3",
    name: "energetic beats",
    url: "/bgm/energetic-beats.mp3",
    category: "Upbeat",
    duration: 150,
    description: "High-energy beats for action scenes",
  },
  {
    id: "4",
    name: "majestic orchestra",
    url: "/bgm/majestic-orchestra.mp3",
    category: "Orchestral",
    duration: 240,
    description: "Grand orchestral composition for epic moments",
  },
  {
    id: "5",
    name: "melancholy instrumental",
    url: "/bgm/melancholy-instrumental.mp3",
    category: "Emotional",
    duration: 200,
    description: "Touching instrumental piece for emotional scenes",
  },
  {
    id: "6",
    name: "serene piano",
    url: "/bgm/serene-piano.mp3",
    category: "Piano",
    duration: 160,
    description: "Peaceful piano melody for calm moments",
  },
  {
    id: "7",
    name: "upbeat chill",
    url: "/bgm/upbeat-chill.mp3",
    category: "Chill",
    duration: 140,
    description: "Uplifting yet relaxed background music",
  },
];

interface BGMSelectorProps {
  className?: string;
}

export const BGMSelector: React.FC<BGMSelectorProps> = ({ className }) => {
  const [bgmList, setBgmList] = useState<BGM[]>([]);
  const [loading, setLoading] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentPreview, setCurrentPreview] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const { selectedBgm } = useTaskStore();

  useEffect(() => {
    fetchBGMList();
  }, []);

  useEffect(() => {
    // 清理音频资源
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.src = "";
      }
    };
  }, []);

  const fetchBGMList = () => {
    try {
      setLoading(true);
      const response = bgmService.getBGMList();
      setBgmList(response.items);
    } catch (err) {
      console.error("Error fetching BGM list:", err);
      setError("Failed to load BGM list");
    } finally {
      setLoading(false);
    }
  };

  const handleBGMChange = (bgmId: string) => {
    // 清除错误提示
    setError(null);

    if (bgmId === "") {
      // 选择"No Background Music"
      useTaskStore.setState({
        selectedBgm: null,
      });
    } else {
      const selectedBGM = bgmList.find((bgm) => bgm.id === bgmId);
      if (selectedBGM) {
        useTaskStore.setState({
          selectedBgm: selectedBGM,
        });
      }
    }
  };

  const handlePreview = (bgm: BGM) => {
    if (currentPreview === bgm.id && isPlaying) {
      // 停止当前播放
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
      setIsPlaying(false);
      setCurrentPreview(null);
    } else {
      // 开始播放新的BGM
      if (audioRef.current) {
        audioRef.current.pause();
      }

      const audio = new Audio(bgm.url);
      audioRef.current = audio;

      audio.addEventListener("ended", () => {
        setIsPlaying(false);
        setCurrentPreview(null);
      });

      audio.addEventListener("error", () => {
        setError(`Failed to load audio: ${bgm.name}`);
        setIsPlaying(false);
        setCurrentPreview(null);
      });

      audio
        .play()
        .then(() => {
          setIsPlaying(true);
          setCurrentPreview(bgm.id);
          setError(null);
        })
        .catch((err) => {
          console.error("Playback error:", err);
          setError("Failed to play audio");
        });
    }
  };

  return (
    <Box
      className={className}
      sx={{
        p: 2,
        bgcolor: "white",
        borderRadius: 2,
        border: "1px solid #E3E6EA",
      }}
    >
      <Typography
        variant="body1"
        sx={{ mb: 1.5, fontWeight: 500, color: "#333", fontSize: "1.4rem" }}
      >
        Background Music
      </Typography>

      {error && (
        <Alert
          severity="error"
          sx={{
            mb: 1.5,
            fontSize: "12px",
            "& .MuiAlert-message": { fontSize: "12px" },
          }}
        >
          {error}
        </Alert>
      )}

      <FormControl fullWidth>
        <Select
          value={selectedBgm?.id || ""}
          onChange={(e) => handleBGMChange(e.target.value)}
          disabled={loading}
          displayEmpty
          renderValue={(value) => {
            if (!value) {
              return (
                <span style={{ fontSize: "1.2rem", color: "#9e9e9e" }}>
                  Select BGM
                </span>
              );
            }
            const bgm = bgmList.find((b) => b.id === value);
            return bgm ? bgm.name : "";
          }}
          sx={{
            "& .MuiSelect-select": {
              py: 1,
              minHeight: "1.4375em", // 确保高度一致
              display: "flex",
              alignItems: "center",
            },
          }}
        >
          <MenuItem value="">
            <em style={{ fontSize: "1.2rem" }}>No Background Music</em>
          </MenuItem>
          {bgmList.map((bgm) => (
            <MenuItem key={bgm.id} value={bgm.id}>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  width: "100%",
                }}
              >
                <Box sx={{ flex: 1 }}>
                  <Typography
                    variant="body2"
                    sx={{ fontWeight: 500, fontSize: "1.2rem" }}
                  >
                    {bgm.name}
                  </Typography>
                </Box>
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation(); // 防止触发MenuItem的选择
                    handlePreview(bgm);
                  }}
                  sx={{
                    ml: 1,
                    bgcolor:
                      currentPreview === bgm.id && isPlaying
                        ? "primary.main"
                        : "grey.100",
                    color:
                      currentPreview === bgm.id && isPlaying
                        ? "white"
                        : "primary.main",
                    "&:hover": {
                      bgcolor:
                        currentPreview === bgm.id && isPlaying
                          ? "primary.dark"
                          : "grey.200",
                    },
                    width: 28,
                    height: 28,
                  }}
                >
                  {currentPreview === bgm.id && isPlaying ? (
                    <Pause fontSize="small" />
                  ) : (
                    <PlayArrow fontSize="small" />
                  )}
                </IconButton>
              </Box>
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </Box>
  );
};
