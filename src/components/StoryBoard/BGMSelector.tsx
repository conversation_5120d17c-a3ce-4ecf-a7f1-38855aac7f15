import React, { useState, useEffect, useRef } from "react";
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  IconButton,
  Typography,
  Alert,
} from "@mui/material";
import { PlayArrow, Pause } from "@mui/icons-material";
import { bgmService, BGM } from "@/services/bgmService";
import { useTaskStore } from "@/store/task";

interface BGMSelectorProps {
  className?: string;
}

export const BGMSelector: React.FC<BGMSelectorProps> = ({ className }) => {
  const [bgmList, setBgmList] = useState<BGM[]>([]);
  const [loading, setLoading] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentPreview, setCurrentPreview] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const { selectedBgm } = useTaskStore();

  useEffect(() => {
    fetchBGMList();
  }, []);

  useEffect(() => {
    // 清理音频资源
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.src = "";
      }
    };
  }, []);

  const fetchBGMList = () => {
    try {
      setLoading(true);
      const response = bgmService.getBGMList();
      setBgmList(response.items);
    } catch (err) {
      console.error("Error fetching BGM list:", err);
      setError("Failed to load BGM list");
    } finally {
      setLoading(false);
    }
  };

  const handleBGMChange = (bgmId: string) => {
    // 清除错误提示
    setError(null);

    if (bgmId === "") {
      // 选择"No Background Music"
      useTaskStore.setState({
        selectedBgm: null,
      });
    } else {
      const selectedBGM = bgmList.find((bgm) => bgm.id === bgmId);
      if (selectedBGM) {
        useTaskStore.setState({
          selectedBgm: selectedBGM,
        });
      }
    }
  };

  const handlePreview = (bgm: BGM) => {
    if (currentPreview === bgm.id && isPlaying) {
      // 停止当前播放
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
      setIsPlaying(false);
      setCurrentPreview(null);
    } else {
      // 开始播放新的BGM
      if (audioRef.current) {
        audioRef.current.pause();
      }

      const audio = new Audio(bgm.url);
      audioRef.current = audio;

      audio.addEventListener("ended", () => {
        setIsPlaying(false);
        setCurrentPreview(null);
      });

      audio.addEventListener("error", () => {
        setError(`Failed to load audio: ${bgm.name}`);
        setIsPlaying(false);
        setCurrentPreview(null);
      });

      audio
        .play()
        .then(() => {
          setIsPlaying(true);
          setCurrentPreview(bgm.id);
          setError(null);
        })
        .catch((err) => {
          console.error("Playback error:", err);
          setError("Failed to play audio");
        });
    }
  };

  return (
    <Box
      className={className}
      sx={{
        p: 3,
        bgcolor: "white",
        borderRadius: 2,
        border: "1px solid #E3E6EA",
      }}
    >
      <Typography
        variant="body1"
        sx={{ mb: 2, fontWeight: 500, color: "#333", fontSize: "14px" }}
      >
        Background Music
      </Typography>

      {error && (
        <Alert
          severity="error"
          sx={{
            mb: 2,
            fontSize: "12px",
            "& .MuiAlert-message": { fontSize: "12px" },
          }}
        >
          {error}
        </Alert>
      )}

      <FormControl fullWidth sx={{ mb: 2 }}>
        <InputLabel>Select BGM</InputLabel>
        <Select
          value={selectedBgm?.id || ""}
          label="Select BGM"
          onChange={(e) => handleBGMChange(e.target.value)}
          disabled={loading}
          displayEmpty
          renderValue={(value) => {
            if (!value) {
              return <em style={{ fontSize: "14px" }}>No Background Music</em>;
            }
            const bgm = bgmList.find((b) => b.id === value);
            return bgm ? bgm.name : "";
          }}
          sx={{
            "& .MuiSelect-select": {
              py: 1,
              minHeight: "1.4375em", // 确保高度一致
              display: "flex",
              alignItems: "center",
            },
          }}
        >
          <MenuItem value="">
            <em style={{ fontSize: "14px" }}>No Background Music</em>
          </MenuItem>
          {bgmList.map((bgm) => (
            <MenuItem key={bgm.id} value={bgm.id}>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  width: "100%",
                }}
              >
                <Box sx={{ flex: 1 }}>
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    {bgm.name}
                  </Typography>
                </Box>
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation(); // 防止触发MenuItem的选择
                    handlePreview(bgm);
                  }}
                  sx={{
                    ml: 1,
                    bgcolor:
                      currentPreview === bgm.id && isPlaying
                        ? "primary.main"
                        : "grey.100",
                    color:
                      currentPreview === bgm.id && isPlaying
                        ? "white"
                        : "primary.main",
                    "&:hover": {
                      bgcolor:
                        currentPreview === bgm.id && isPlaying
                          ? "primary.dark"
                          : "grey.200",
                    },
                    width: 28,
                    height: 28,
                  }}
                >
                  {currentPreview === bgm.id && isPlaying ? (
                    <Pause fontSize="small" />
                  ) : (
                    <PlayArrow fontSize="small" />
                  )}
                </IconButton>
              </Box>
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </Box>
  );
};
