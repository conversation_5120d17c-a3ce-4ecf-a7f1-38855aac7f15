import React, { useState, useEffect, useRef } from "react";
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  IconButton,
  Typography,
  Chip,
  Alert,
} from "@mui/material";
import { PlayArrow, Pause, VolumeUp, VolumeOff } from "@mui/icons-material";
import { bgmService, BGM } from "@/services/bgmService";
import { useTaskStore } from "@/store/task";

interface BGMSelectorProps {
  className?: string;
}

export const BGMSelector: React.FC<BGMSelectorProps> = ({ className }) => {
  const [bgmList, setBgmList] = useState<BGM[]>([]);
  const [loading, setLoading] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentPreview, setCurrentPreview] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const { selectedBgm } = useTaskStore();

  useEffect(() => {
    fetchBGMList();
  }, []);

  useEffect(() => {
    // 清理音频资源
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.src = "";
      }
    };
  }, []);

  const fetchBGMList = () => {
    try {
      setLoading(true);
      const response = bgmService.getBGMList();
      setBgmList(response.items);
    } catch (err) {
      console.error("Error fetching BGM list:", err);
      setError("Failed to load BGM list");
    } finally {
      setLoading(false);
    }
  };

  const handleBGMChange = (bgmId: string) => {
    const selectedBGM = bgmList.find((bgm) => bgm.id === bgmId);
    if (selectedBGM) {
      useTaskStore.setState({
        selectedBgm: selectedBGM,
      });
    }
  };

  const handlePreview = (bgm: BGM) => {
    if (currentPreview === bgm.id && isPlaying) {
      // 停止当前播放
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
      setIsPlaying(false);
      setCurrentPreview(null);
    } else {
      // 开始播放新的BGM
      if (audioRef.current) {
        audioRef.current.pause();
      }

      const audio = new Audio(bgm.url);
      audioRef.current = audio;

      audio.addEventListener("ended", () => {
        setIsPlaying(false);
        setCurrentPreview(null);
      });

      audio.addEventListener("error", () => {
        setError(`Failed to load audio: ${bgm.name}`);
        setIsPlaying(false);
        setCurrentPreview(null);
      });

      audio
        .play()
        .then(() => {
          setIsPlaying(true);
          setCurrentPreview(bgm.id);
          setError(null);
        })
        .catch((err) => {
          console.error("Playback error:", err);
          setError("Failed to play audio");
        });
    }
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return "";
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      Rhythmic: "error",
      Electronic: "info",
      Upbeat: "success",
      Orchestral: "primary",
      Emotional: "secondary",
      Piano: "warning",
      Chill: "default",
    };
    return colors[category] || "default";
  };

  return (
    <Box
      className={className}
      sx={{
        p: 3,
        bgcolor: "white",
        borderRadius: 2,
        border: "1px solid #E3E6EA",
      }}
    >
      <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: "#333" }}>
        Background Music
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <FormControl fullWidth sx={{ mb: 2 }}>
        <InputLabel>Select BGM</InputLabel>
        <Select
          value={selectedBgm?.id || ""}
          label="Select BGM"
          onChange={(e) => handleBGMChange(e.target.value)}
          disabled={loading}
        >
          <MenuItem value="">
            <em>No Background Music</em>
          </MenuItem>
          {bgmList.map((bgm) => (
            <MenuItem key={bgm.id} value={bgm.id}>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  width: "100%",
                }}
              >
                <Box>
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    {bgm.name}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {bgm.description}
                  </Typography>
                </Box>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Chip
                    label={bgm.category}
                    size="small"
                    color={getCategoryColor(bgm.category) as any}
                    variant="outlined"
                  />
                  {bgm.duration && (
                    <Typography variant="caption" color="text.secondary">
                      {formatDuration(bgm.duration)}
                    </Typography>
                  )}
                </Box>
              </Box>
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {selectedBgm && (
        <Box
          sx={{
            p: 2,
            bgcolor: "#f5f5f5",
            borderRadius: 1,
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Box>
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              {selectedBgm.name}
            </Typography>
            <Box
              sx={{ display: "flex", alignItems: "center", gap: 1, mt: 0.5 }}
            >
              <Chip
                label={selectedBgm.category}
                size="small"
                color={getCategoryColor(selectedBgm.category) as any}
                variant="outlined"
              />
              {selectedBgm.duration && (
                <Typography variant="caption" color="text.secondary">
                  {formatDuration(selectedBgm.duration)}
                </Typography>
              )}
            </Box>
          </Box>

          <IconButton
            onClick={() => handlePreview(selectedBgm)}
            sx={{
              bgcolor: "primary.main",
              color: "white",
              "&:hover": {
                bgcolor: "primary.dark",
              },
            }}
          >
            {currentPreview === selectedBgm.id && isPlaying ? (
              <Pause />
            ) : (
              <PlayArrow />
            )}
          </IconButton>
        </Box>
      )}
    </Box>
  );
};
