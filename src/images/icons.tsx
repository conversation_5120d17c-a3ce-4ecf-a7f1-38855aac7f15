// Mui Import
import { Box } from '@mui/material';

export const EditIcon = ({
  width,
  height
}: {
  width?: number;
  height?: number;
}) => (
  <Box style={{ marginTop: '10px' }}>
    <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
      width={width ?? '60'}
      height={height ?? '60'}>
      <path d="M930.6672 228.010394L796.091945 93.3328c-16.271837-16.271837-37.865281-25.175295-60.789127-25.175295-23.026184 0-44.619628 8.903458-60.789126 25.175295L110.8327 656.911453c-5.321607 5.321607-8.801119 12.280632-9.824505 19.751349L68.464521 916.646812c-1.43274 10.745553 2.149111 21.491105 9.824506 29.064161 6.54967 6.54967 15.35079 10.131521 24.458924 10.131522 1.535079 0 3.070158-0.102339 4.605237-0.307016l239.98401-32.543674c7.470718-1.023386 14.429742-4.502898 19.751349-9.824505l563.476314-563.476315c16.271837-16.271837 25.175295-37.865281 25.175295-60.789126 0.102339-23.026184-8.801119-44.619628-25.072956-60.891465zM326.460124 855.960024l-183.390766 24.868279 24.868279-183.390766 399.325205-399.325204 158.522487 158.522486L326.460124 855.960024z m555.289226-555.186888L774.703178 407.819308 616.28303 249.296822 723.226864 142.25065c4.298221-4.298221 9.312812-4.912253 11.973616-4.912253s7.675395 0.614032 11.973616 4.912253l134.575254 134.575254c4.298221 4.298221 4.912253 9.312812 4.912253 11.973616 0 2.660804-0.614032 7.675395-4.912253 11.973616z" fill="#1976d2" p-id="1539">
      </path>
    </svg>
  </Box>
);

export const EditButtonIcon = ({
  width,
  height,
  onClick
}: {
  width?: number;
  height?: number;
  onClick?: () => void;
}) => (
  <Box style={{ marginTop: '10px' }}>
    <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width={width ?? '60'}
      height={height ?? '60'}>
      <path d="M860 504c-19.9 0-36 16.1-36 36 0 1.4 0.1 2.7 0.2 4h-0.2v344H136V200h376c19.9 0 36-16.1 36-36s-16.1-36-36-36H136c-39.8 0-72 32.2-72 72v688c0 39.8 32.2 72 72 72h688c39.8 0 72-32.2 72-72V544h-0.2c0.1-1.3 0.2-2.6 0.2-4 0-19.9-16.1-36-36-36z" p-id="3654" fill="#ffffff"></path><path d="M1002.7 100.3L923.4 21c-28.1-28.1-73.9-27.9-102 0.2L424.2 418.4c-2.9 2.9-5.2 6.4-6.8 10.2L317.6 664c-5.6 13.2-1.7 26.5 6.8 35.1 8.5 8.6 21.9 12.5 35.2 6.9l235.5-99.7c3.8-1.6 7.2-3.9 10.2-6.8l397.2-397.2c28.1-28.1 28.3-73.9 0.2-102zM559.8 543l-137.4 58.2 58.2-137.4L759.4 185l79.2 79.2L559.8 543z m391.7-391.7l-62 62-79.2-79.2 62-62 0.2-0.2 79.2 79.2-0.2 0.2z" p-id="3655" fill="#ffffff">
      </path>
    </svg>
  </Box>
);

export const UserIcon = ({
  width,
  height
}: {
  width?: number;
  height?: number;
}) => (
  <Box style={{ marginTop: '10px' }}>
    <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
      width={width ?? '60'}
      height={height ?? '60'}>
      <path d="M691.2 569.6c81.067-55.467 134.4-149.333 134.4-256C825.6 140.8 684.8 0 512 0S200.533 140.8 200.533 311.467c0 106.666 53.334 200.533 134.4 256C151.467 640 23.467 817.067 23.467 1024h87.466c0-204.8 155.734-375.467 354.134-398.933 2.133 0 55.466-4.267 100.266 0h4.267c194.133 27.733 343.467 194.133 343.467 396.8h89.6C1000.533 817.067 872.533 640 691.2 569.6zM512 546.133c-130.133 0-234.667-104.533-234.667-234.666C277.333 181.333 381.867 76.8 512 76.8c130.133 0 234.667 104.533 234.667 234.667 0 130.133-104.534 234.666-234.667 234.666z" p-id="4683" fill="#1976d2">
      </path>
    </svg>
  </Box>
);

export const FrameIcon = ({
  width,
  height
}: {
  width?: number;
  height?: number;
}) => (
  <Box style={{ marginTop: '10px' }}>
    <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
      width={width ?? '60'}
      height={height ?? '60'}>
      <path d="M85.333333 170.368A42.666667 42.666667 0 0 1 127.658667 128h768.682666c23.381333 0 42.325333 18.986667 42.325334 42.368v683.264a42.666667 42.666667 0 0 1-42.325334 42.368H127.658667A42.368 42.368 0 0 1 85.333333 853.632V170.368zM341.333333 213.333333v597.333334h341.333334V213.333333H341.333333zM170.666667 213.333333v85.333334h85.333333V213.333333H170.666667z m597.333333 0v85.333334h85.333333V213.333333h-85.333333zM170.666667 384v85.333333h85.333333V384H170.666667z m597.333333 0v85.333333h85.333333V384h-85.333333zM170.666667 554.666667v85.333333h85.333333v-85.333333H170.666667z m597.333333 0v85.333333h85.333333v-85.333333h-85.333333zM170.666667 725.333333v85.333334h85.333333v-85.333334H170.666667z m597.333333 0v85.333334h85.333333v-85.333334h-85.333333z" p-id="7657" fill="#1976d2">
      </path>
    </svg>
  </Box>
);